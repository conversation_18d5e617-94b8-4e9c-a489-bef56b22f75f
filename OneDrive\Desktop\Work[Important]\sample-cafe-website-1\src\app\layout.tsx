import type { Metada<PERSON> } from "next";
import "./globals.css";
import GooeyNav from "../components/GooeyNav/GooeyNav";


export const metadata: Metadata = {
  title: "Sample Cafe Website",
  description: "Created by prat<PERSON><PERSON> pushkar",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const items = [
    {label: "Home", href: "#" },
    {label: "About", href: "#" },
    {label: "Contact", href: "#" },
  ];

  return (
    <html lang="en">
      <body suppressHydrationWarning>
        <div className="navigation">
          <GooeyNav
            items={items}
            particleCount={25}
            particleDistances={[90, 10]}
            particleR={100}
            initialActiveIndex={0}
            animationTime={600}
            timeVariance={300}
            colors={[1, 2, 3, 1, 2, 3, 1, 4]}
          />
        </div>
        {children}
      </body>
    </html>
  );
}
