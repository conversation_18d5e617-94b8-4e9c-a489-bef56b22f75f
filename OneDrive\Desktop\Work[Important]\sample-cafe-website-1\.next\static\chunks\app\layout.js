/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Ccomponents%5C%5CGooeyNav%5C%5CGooeyNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Ccomponents%5C%5CGooeyNav%5C%5CGooeyNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/GooeyNav/GooeyNav.tsx */ \"(app-pages-browser)/./src/components/GooeyNav/GooeyNav.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGV3cGV3JTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDV29yayU1QkltcG9ydGFudCU1RCU1QyU1Q3NhbXBsZS1jYWZlLXdlYnNpdGUtMSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3Bld3BldyU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q1dvcmslNUJJbXBvcnRhbnQlNUQlNUMlNUNzYW1wbGUtY2FmZS13ZWJzaXRlLTElNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDR29vZXlOYXYlNUMlNUNHb29leU5hdi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQXdJO0FBQ3hJO0FBQ0Esc01BQXVMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwZXdwZXdcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxXb3JrW0ltcG9ydGFudF1cXFxcc2FtcGxlLWNhZmUtd2Vic2l0ZS0xXFxcXHNyY1xcXFxhcHBcXFxcZ2xvYmFscy5jc3NcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxwZXdwZXdcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxXb3JrW0ltcG9ydGFudF1cXFxcc2FtcGxlLWNhZmUtd2Vic2l0ZS0xXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXEdvb2V5TmF2XFxcXEdvb2V5TmF2LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Ccomponents%5C%5CGooeyNav%5C%5CGooeyNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGV3cGV3XFxPbmVEcml2ZVxcRGVza3RvcFxcV29ya1tJbXBvcnRhbnRdXFxzYW1wbGUtY2FmZS13ZWJzaXRlLTFcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cc381440dcc1\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBld3Bld1xcT25lRHJpdmVcXERlc2t0b3BcXFdvcmtbSW1wb3J0YW50XVxcc2FtcGxlLWNhZmUtd2Vic2l0ZS0xXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjYzM4MTQ0MGRjYzFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/GooeyNav/GooeyNav.css":
/*!**********************************************!*\
  !*** ./src/components/GooeyNav/GooeyNav.css ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3bfca87b3861\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0dvb2V5TmF2L0dvb2V5TmF2LmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGV3cGV3XFxPbmVEcml2ZVxcRGVza3RvcFxcV29ya1tJbXBvcnRhbnRdXFxzYW1wbGUtY2FmZS13ZWJzaXRlLTFcXHNyY1xcY29tcG9uZW50c1xcR29vZXlOYXZcXEdvb2V5TmF2LmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjNiZmNhODdiMzg2MVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/GooeyNav/GooeyNav.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/GooeyNav/GooeyNav.tsx":
/*!**********************************************!*\
  !*** ./src/components/GooeyNav/GooeyNav.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _GooeyNav_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GooeyNav.css */ \"(app-pages-browser)/./src/components/GooeyNav/GooeyNav.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n/*\n\tInstalled from https://reactbits.dev/ts/default/\n*/ \n\nconst GooeyNav = (param)=>{\n    let { items, animationTime = 600, particleCount = 15, particleDistances = [\n        90,\n        10\n    ], particleR = 100, timeVariance = 300, colors = [\n        1,\n        2,\n        3,\n        1,\n        2,\n        3,\n        1,\n        4\n    ], initialActiveIndex = 0 } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const navRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const filterRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialActiveIndex);\n    const noise = function() {\n        let n = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return n / 2 - Math.random() * n;\n    };\n    const getXY = (distance, pointIndex, totalPoints)=>{\n        const angle = (360 + noise(8)) / totalPoints * pointIndex * (Math.PI / 180);\n        return [\n            distance * Math.cos(angle),\n            distance * Math.sin(angle)\n        ];\n    };\n    const createParticle = (i, t, d, r)=>{\n        const rotate = noise(r / 10);\n        return {\n            start: getXY(d[0], particleCount - i, particleCount),\n            end: getXY(d[1] + noise(7), particleCount - i, particleCount),\n            time: t,\n            scale: 1 + noise(0.2),\n            color: colors[Math.floor(Math.random() * colors.length)],\n            rotate: rotate > 0 ? (rotate + r / 20) * 10 : (rotate - r / 20) * 10\n        };\n    };\n    const makeParticles = (element)=>{\n        const d = particleDistances;\n        const r = particleR;\n        const bubbleTime = animationTime * 2 + timeVariance;\n        element.style.setProperty(\"--time\", \"\".concat(bubbleTime, \"ms\"));\n        for(let i = 0; i < particleCount; i++){\n            const t = animationTime * 2 + noise(timeVariance * 2);\n            const p = createParticle(i, t, d, r);\n            element.classList.remove(\"active\");\n            setTimeout(()=>{\n                const particle = document.createElement(\"span\");\n                const point = document.createElement(\"span\");\n                particle.classList.add(\"particle\");\n                particle.style.setProperty(\"--start-x\", \"\".concat(p.start[0], \"px\"));\n                particle.style.setProperty(\"--start-y\", \"\".concat(p.start[1], \"px\"));\n                particle.style.setProperty(\"--end-x\", \"\".concat(p.end[0], \"px\"));\n                particle.style.setProperty(\"--end-y\", \"\".concat(p.end[1], \"px\"));\n                particle.style.setProperty(\"--time\", \"\".concat(p.time, \"ms\"));\n                particle.style.setProperty(\"--scale\", \"\".concat(p.scale));\n                particle.style.setProperty(\"--color\", \"var(--color-\".concat(p.color, \", white)\"));\n                particle.style.setProperty(\"--rotate\", \"\".concat(p.rotate, \"deg\"));\n                point.classList.add(\"point\");\n                particle.appendChild(point);\n                element.appendChild(particle);\n                requestAnimationFrame(()=>{\n                    element.classList.add(\"active\");\n                });\n                setTimeout(()=>{\n                    try {\n                        element.removeChild(particle);\n                    } catch (e) {\n                    // Do nothing\n                    }\n                }, t);\n            }, 30);\n        }\n    };\n    const updateEffectPosition = (element)=>{\n        if (!containerRef.current || !filterRef.current || !textRef.current) return;\n        const containerRect = containerRef.current.getBoundingClientRect();\n        const pos = element.getBoundingClientRect();\n        const styles = {\n            left: \"\".concat(pos.x - containerRect.x, \"px\"),\n            top: \"\".concat(pos.y - containerRect.y, \"px\"),\n            width: \"\".concat(pos.width, \"px\"),\n            height: \"\".concat(pos.height, \"px\")\n        };\n        Object.assign(filterRef.current.style, styles);\n        Object.assign(textRef.current.style, styles);\n        textRef.current.innerText = element.innerText;\n    };\n    const handleClick = (e, index)=>{\n        const liEl = e.currentTarget;\n        if (activeIndex === index) return;\n        setActiveIndex(index);\n        updateEffectPosition(liEl);\n        if (filterRef.current) {\n            const particles = filterRef.current.querySelectorAll(\".particle\");\n            particles.forEach((p)=>filterRef.current.removeChild(p));\n        }\n        if (textRef.current) {\n            textRef.current.classList.remove(\"active\");\n            void textRef.current.offsetWidth;\n            textRef.current.classList.add(\"active\");\n        }\n        if (filterRef.current) {\n            makeParticles(filterRef.current);\n        }\n    };\n    const handleKeyDown = (e, index)=>{\n        if (e.key === \"Enter\" || e.key === \" \") {\n            e.preventDefault();\n            const liEl = e.currentTarget.parentElement;\n            if (liEl) {\n                handleClick({\n                    currentTarget: liEl\n                }, index);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GooeyNav.useEffect\": ()=>{\n            if (!navRef.current || !containerRef.current) return;\n            const activeLi = navRef.current.querySelectorAll(\"li\")[activeIndex];\n            if (activeLi) {\n                var _textRef_current;\n                updateEffectPosition(activeLi);\n                (_textRef_current = textRef.current) === null || _textRef_current === void 0 ? void 0 : _textRef_current.classList.add(\"active\");\n            }\n            const resizeObserver = new ResizeObserver({\n                \"GooeyNav.useEffect\": ()=>{\n                    var _navRef_current;\n                    const currentActiveLi = (_navRef_current = navRef.current) === null || _navRef_current === void 0 ? void 0 : _navRef_current.querySelectorAll(\"li\")[activeIndex];\n                    if (currentActiveLi) {\n                        updateEffectPosition(currentActiveLi);\n                    }\n                }\n            }[\"GooeyNav.useEffect\"]);\n            resizeObserver.observe(containerRef.current);\n            return ({\n                \"GooeyNav.useEffect\": ()=>resizeObserver.disconnect()\n            })[\"GooeyNav.useEffect\"];\n        }\n    }[\"GooeyNav.useEffect\"], [\n        activeIndex\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"gooey-nav-container\",\n        ref: containerRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    ref: navRef,\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: activeIndex === index ? \"active\" : \"\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: item.href,\n                                onClick: (e)=>handleClick(e, index),\n                                onKeyDown: (e)=>handleKeyDown(e, index),\n                                children: item.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\GooeyNav\\\\GooeyNav.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, undefined)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\GooeyNav\\\\GooeyNav.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\GooeyNav\\\\GooeyNav.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\GooeyNav\\\\GooeyNav.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"effect filter\",\n                ref: filterRef\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\GooeyNav\\\\GooeyNav.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"effect text\",\n                ref: textRef\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\GooeyNav\\\\GooeyNav.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\sample-cafe-website-1\\\\src\\\\components\\\\GooeyNav\\\\GooeyNav.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GooeyNav, \"q8egYqNxX0HAskGPquyzqhCBCuo=\");\n_c = GooeyNav;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GooeyNav);\nvar _c;\n$RefreshReg$(_c, \"GooeyNav\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0dvb2V5TmF2L0dvb2V5TmF2LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRUE7O0FBRUEsR0FFMkQ7QUFDbkM7QUFrQnhCLE1BQU1JLFdBQW9DO1FBQUMsRUFDekNDLEtBQUssRUFDTEMsZ0JBQWdCLEdBQUcsRUFDbkJDLGdCQUFnQixFQUFFLEVBQ2xCQyxvQkFBb0I7UUFBQztRQUFJO0tBQUcsRUFDNUJDLFlBQVksR0FBRyxFQUNmQyxlQUFlLEdBQUcsRUFDbEJDLFNBQVM7UUFBQztRQUFHO1FBQUc7UUFBRztRQUFHO1FBQUc7UUFBRztRQUFHO0tBQUUsRUFDakNDLHFCQUFxQixDQUFDLEVBQ3ZCOztJQUNDLE1BQU1DLGVBQWVaLDZDQUFNQSxDQUFpQjtJQUM1QyxNQUFNYSxTQUFTYiw2Q0FBTUEsQ0FBbUI7SUFDeEMsTUFBTWMsWUFBWWQsNkNBQU1BLENBQWtCO0lBQzFDLE1BQU1lLFVBQVVmLDZDQUFNQSxDQUFrQjtJQUN4QyxNQUFNLENBQUNnQixhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFTUztJQUV2RCxNQUFNTyxRQUFRO1lBQUNDLHFFQUFJO2VBQU1BLElBQUksSUFBSUMsS0FBS0MsTUFBTSxLQUFLRjs7SUFFakQsTUFBTUcsUUFBUSxDQUNaQyxVQUNBQyxZQUNBQztRQUVBLE1BQU1DLFFBQ0osQ0FBRSxNQUFNUixNQUFNLEVBQUMsSUFBS08sY0FBZUQsYUFBY0osQ0FBQUEsS0FBS08sRUFBRSxHQUFHLEdBQUU7UUFDL0QsT0FBTztZQUFDSixXQUFXSCxLQUFLUSxHQUFHLENBQUNGO1lBQVFILFdBQVdILEtBQUtTLEdBQUcsQ0FBQ0g7U0FBTztJQUNqRTtJQUVBLE1BQU1JLGlCQUFpQixDQUNyQkMsR0FDQUMsR0FDQUMsR0FDQUM7UUFFQyxNQUFNQyxTQUFTakIsTUFBTWdCLElBQUk7UUFDMUIsT0FBTztZQUNMRSxPQUFPZCxNQUFNVyxDQUFDLENBQUMsRUFBRSxFQUFFM0IsZ0JBQWdCeUIsR0FBR3pCO1lBQ3RDK0IsS0FBS2YsTUFBTVcsQ0FBQyxDQUFDLEVBQUUsR0FBR2YsTUFBTSxJQUFJWixnQkFBZ0J5QixHQUFHekI7WUFDL0NnQyxNQUFNTjtZQUNOTyxPQUFPLElBQUlyQixNQUFNO1lBQ2pCc0IsT0FBTzlCLE1BQU0sQ0FBQ1UsS0FBS3FCLEtBQUssQ0FBQ3JCLEtBQUtDLE1BQU0sS0FBS1gsT0FBT2dDLE1BQU0sRUFBRTtZQUN4RFAsUUFBUUEsU0FBUyxJQUFJLENBQUNBLFNBQVNELElBQUksRUFBQyxJQUFLLEtBQUssQ0FBQ0MsU0FBU0QsSUFBSSxFQUFDLElBQUs7UUFDcEU7SUFDRjtJQUVBLE1BQU1TLGdCQUFnQixDQUFDQztRQUNyQixNQUFNWCxJQUFzQjFCO1FBQzVCLE1BQU0yQixJQUFJMUI7UUFDVixNQUFNcUMsYUFBYXhDLGdCQUFnQixJQUFJSTtRQUN2Q21DLFFBQVFFLEtBQUssQ0FBQ0MsV0FBVyxDQUFDLFVBQVUsR0FBYyxPQUFYRixZQUFXO1FBRWxELElBQUssSUFBSWQsSUFBSSxHQUFHQSxJQUFJekIsZUFBZXlCLElBQUs7WUFDdEMsTUFBTUMsSUFBSTNCLGdCQUFnQixJQUFJYSxNQUFNVCxlQUFlO1lBQ25ELE1BQU11QyxJQUFJbEIsZUFBZUMsR0FBR0MsR0FBR0MsR0FBR0M7WUFDbENVLFFBQVFLLFNBQVMsQ0FBQ0MsTUFBTSxDQUFDO1lBRXpCQyxXQUFXO2dCQUNULE1BQU1DLFdBQVdDLFNBQVNDLGFBQWEsQ0FBQztnQkFDeEMsTUFBTUMsUUFBUUYsU0FBU0MsYUFBYSxDQUFDO2dCQUNyQ0YsU0FBU0gsU0FBUyxDQUFDTyxHQUFHLENBQUM7Z0JBQ3ZCSixTQUFTTixLQUFLLENBQUNDLFdBQVcsQ0FBQyxhQUFhLEdBQWMsT0FBWEMsRUFBRVosS0FBSyxDQUFDLEVBQUUsRUFBQztnQkFDdERnQixTQUFTTixLQUFLLENBQUNDLFdBQVcsQ0FBQyxhQUFhLEdBQWMsT0FBWEMsRUFBRVosS0FBSyxDQUFDLEVBQUUsRUFBQztnQkFDdERnQixTQUFTTixLQUFLLENBQUNDLFdBQVcsQ0FBQyxXQUFXLEdBQVksT0FBVEMsRUFBRVgsR0FBRyxDQUFDLEVBQUUsRUFBQztnQkFDbERlLFNBQVNOLEtBQUssQ0FBQ0MsV0FBVyxDQUFDLFdBQVcsR0FBWSxPQUFUQyxFQUFFWCxHQUFHLENBQUMsRUFBRSxFQUFDO2dCQUNsRGUsU0FBU04sS0FBSyxDQUFDQyxXQUFXLENBQUMsVUFBVSxHQUFVLE9BQVBDLEVBQUVWLElBQUksRUFBQztnQkFDL0NjLFNBQVNOLEtBQUssQ0FBQ0MsV0FBVyxDQUFDLFdBQVcsR0FBVyxPQUFSQyxFQUFFVCxLQUFLO2dCQUNoRGEsU0FBU04sS0FBSyxDQUFDQyxXQUFXLENBQUMsV0FBVyxlQUF1QixPQUFSQyxFQUFFUixLQUFLLEVBQUM7Z0JBQzdEWSxTQUFTTixLQUFLLENBQUNDLFdBQVcsQ0FBQyxZQUFZLEdBQVksT0FBVEMsRUFBRWIsTUFBTSxFQUFDO2dCQUVuRG9CLE1BQU1OLFNBQVMsQ0FBQ08sR0FBRyxDQUFDO2dCQUNwQkosU0FBU0ssV0FBVyxDQUFDRjtnQkFDckJYLFFBQVFhLFdBQVcsQ0FBQ0w7Z0JBQ3BCTSxzQkFBc0I7b0JBQ3BCZCxRQUFRSyxTQUFTLENBQUNPLEdBQUcsQ0FBQztnQkFDeEI7Z0JBQ0FMLFdBQVc7b0JBQ1QsSUFBSTt3QkFDRlAsUUFBUWUsV0FBVyxDQUFDUDtvQkFDdEIsRUFBRSxVQUFNO29CQUNOLGFBQWE7b0JBQ2Y7Z0JBQ0YsR0FBR3BCO1lBQ0wsR0FBRztRQUNMO0lBQ0Y7SUFFQSxNQUFNNEIsdUJBQXVCLENBQUNoQjtRQUM1QixJQUFJLENBQUNoQyxhQUFhaUQsT0FBTyxJQUFJLENBQUMvQyxVQUFVK0MsT0FBTyxJQUFJLENBQUM5QyxRQUFROEMsT0FBTyxFQUFFO1FBQ3JFLE1BQU1DLGdCQUFnQmxELGFBQWFpRCxPQUFPLENBQUNFLHFCQUFxQjtRQUNoRSxNQUFNQyxNQUFNcEIsUUFBUW1CLHFCQUFxQjtRQUV6QyxNQUFNRSxTQUFTO1lBQ2JDLE1BQU0sR0FBMkIsT0FBeEJGLElBQUlHLENBQUMsR0FBR0wsY0FBY0ssQ0FBQyxFQUFDO1lBQ2pDQyxLQUFLLEdBQTJCLE9BQXhCSixJQUFJSyxDQUFDLEdBQUdQLGNBQWNPLENBQUMsRUFBQztZQUNoQ0MsT0FBTyxHQUFhLE9BQVZOLElBQUlNLEtBQUssRUFBQztZQUNwQkMsUUFBUSxHQUFjLE9BQVhQLElBQUlPLE1BQU0sRUFBQztRQUN4QjtRQUNBQyxPQUFPQyxNQUFNLENBQUMzRCxVQUFVK0MsT0FBTyxDQUFDZixLQUFLLEVBQUVtQjtRQUN2Q08sT0FBT0MsTUFBTSxDQUFDMUQsUUFBUThDLE9BQU8sQ0FBQ2YsS0FBSyxFQUFFbUI7UUFDckNsRCxRQUFROEMsT0FBTyxDQUFDYSxTQUFTLEdBQUc5QixRQUFROEIsU0FBUztJQUMvQztJQUVBLE1BQU1DLGNBQWMsQ0FDbEJDLEdBQ0FDO1FBRUEsTUFBTUMsT0FBT0YsRUFBRUcsYUFBYTtRQUM1QixJQUFJL0QsZ0JBQWdCNkQsT0FBTztRQUUzQjVELGVBQWU0RDtRQUNmakIscUJBQXFCa0I7UUFFckIsSUFBSWhFLFVBQVUrQyxPQUFPLEVBQUU7WUFDckIsTUFBTW1CLFlBQVlsRSxVQUFVK0MsT0FBTyxDQUFDb0IsZ0JBQWdCLENBQUM7WUFDckRELFVBQVVFLE9BQU8sQ0FBQyxDQUFDbEMsSUFBTWxDLFVBQVUrQyxPQUFPLENBQUVGLFdBQVcsQ0FBQ1g7UUFDMUQ7UUFFQSxJQUFJakMsUUFBUThDLE9BQU8sRUFBRTtZQUNuQjlDLFFBQVE4QyxPQUFPLENBQUNaLFNBQVMsQ0FBQ0MsTUFBTSxDQUFDO1lBRWpDLEtBQUtuQyxRQUFROEMsT0FBTyxDQUFDc0IsV0FBVztZQUNoQ3BFLFFBQVE4QyxPQUFPLENBQUNaLFNBQVMsQ0FBQ08sR0FBRyxDQUFDO1FBQ2hDO1FBRUEsSUFBSTFDLFVBQVUrQyxPQUFPLEVBQUU7WUFDckJsQixjQUFjN0IsVUFBVStDLE9BQU87UUFDakM7SUFDRjtJQUVBLE1BQU11QixnQkFBZ0IsQ0FDcEJSLEdBQ0FDO1FBRUEsSUFBSUQsRUFBRVMsR0FBRyxLQUFLLFdBQVdULEVBQUVTLEdBQUcsS0FBSyxLQUFLO1lBQ3RDVCxFQUFFVSxjQUFjO1lBQ2hCLE1BQU1SLE9BQU9GLEVBQUVHLGFBQWEsQ0FBQ1EsYUFBYTtZQUMxQyxJQUFJVCxNQUFNO2dCQUNSSCxZQUNFO29CQUNFSSxlQUFlRDtnQkFDakIsR0FDQUQ7WUFFSjtRQUNGO0lBQ0Y7SUFFQTVFLGdEQUFTQTs4QkFBQztZQUNSLElBQUksQ0FBQ1ksT0FBT2dELE9BQU8sSUFBSSxDQUFDakQsYUFBYWlELE9BQU8sRUFBRTtZQUM5QyxNQUFNMkIsV0FBVzNFLE9BQU9nRCxPQUFPLENBQUNvQixnQkFBZ0IsQ0FBQyxLQUFLLENBQ3BEakUsWUFDRDtZQUNELElBQUl3RSxVQUFVO29CQUVaekU7Z0JBREE2QyxxQkFBcUI0QjtpQkFDckJ6RSxtQkFBQUEsUUFBUThDLE9BQU8sY0FBZjlDLHVDQUFBQSxpQkFBaUJrQyxTQUFTLENBQUNPLEdBQUcsQ0FBQztZQUNqQztZQUVBLE1BQU1pQyxpQkFBaUIsSUFBSUM7c0NBQWU7d0JBQ2hCN0U7b0JBQXhCLE1BQU04RSxtQkFBa0I5RSxrQkFBQUEsT0FBT2dELE9BQU8sY0FBZGhELHNDQUFBQSxnQkFBZ0JvRSxnQkFBZ0IsQ0FBQyxLQUFLLENBQzVEakUsWUFDRDtvQkFDRCxJQUFJMkUsaUJBQWlCO3dCQUNuQi9CLHFCQUFxQitCO29CQUN2QjtnQkFDRjs7WUFFQUYsZUFBZUcsT0FBTyxDQUFDaEYsYUFBYWlELE9BQU87WUFDM0M7c0NBQU8sSUFBTTRCLGVBQWVJLFVBQVU7O1FBQ3hDOzZCQUFHO1FBQUM3RTtLQUFZO0lBRWhCLHFCQUNFLDhEQUFDOEU7UUFBSUMsV0FBVTtRQUFzQkMsS0FBS3BGOzswQkFDeEMsOERBQUNxRjswQkFDQyw0RUFBQ0M7b0JBQUdGLEtBQUtuRjs4QkFDTlQsTUFBTStGLEdBQUcsQ0FBQyxDQUFDQyxNQUFNdkIsc0JBQ2hCLDhEQUFDd0I7NEJBQWVOLFdBQVcvRSxnQkFBZ0I2RCxRQUFRLFdBQVc7c0NBQzVELDRFQUFDeUI7Z0NBQ0NDLE1BQU1ILEtBQUtHLElBQUk7Z0NBQ2ZDLFNBQVMsQ0FBQzVCLElBQU1ELFlBQVlDLEdBQUdDO2dDQUMvQjRCLFdBQVcsQ0FBQzdCLElBQU1RLGNBQWNSLEdBQUdDOzBDQUVsQ3VCLEtBQUtNLEtBQUs7Ozs7OzsyQkFOTjdCOzs7Ozs7Ozs7Ozs7Ozs7MEJBWWYsOERBQUM4QjtnQkFBS1osV0FBVTtnQkFBZ0JDLEtBQUtsRjs7Ozs7OzBCQUNyQyw4REFBQzZGO2dCQUFLWixXQUFVO2dCQUFjQyxLQUFLakY7Ozs7Ozs7Ozs7OztBQUd6QztHQS9MTVo7S0FBQUE7QUFpTU4saUVBQWVBLFFBQVFBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGV3cGV3XFxPbmVEcml2ZVxcRGVza3RvcFxcV29ya1tJbXBvcnRhbnRdXFxzYW1wbGUtY2FmZS13ZWJzaXRlLTFcXHNyY1xcY29tcG9uZW50c1xcR29vZXlOYXZcXEdvb2V5TmF2LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuLypcblx0SW5zdGFsbGVkIGZyb20gaHR0cHM6Ly9yZWFjdGJpdHMuZGV2L3RzL2RlZmF1bHQvXG4qL1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlUmVmLCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgXCIuL0dvb2V5TmF2LmNzc1wiO1xuXG5pbnRlcmZhY2UgR29vZXlOYXZJdGVtIHtcbiAgbGFiZWw6IHN0cmluZztcbiAgaHJlZjogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEdvb2V5TmF2UHJvcHMge1xuICBpdGVtczogR29vZXlOYXZJdGVtW107XG4gIGFuaW1hdGlvblRpbWU/OiBudW1iZXI7XG4gIHBhcnRpY2xlQ291bnQ/OiBudW1iZXI7XG4gIHBhcnRpY2xlRGlzdGFuY2VzPzogW251bWJlciwgbnVtYmVyXTtcbiAgcGFydGljbGVSPzogbnVtYmVyO1xuICB0aW1lVmFyaWFuY2U/OiBudW1iZXI7XG4gIGNvbG9ycz86IG51bWJlcltdO1xuICBpbml0aWFsQWN0aXZlSW5kZXg/OiBudW1iZXI7XG59XG5cbmNvbnN0IEdvb2V5TmF2OiBSZWFjdC5GQzxHb29leU5hdlByb3BzPiA9ICh7XG4gIGl0ZW1zLFxuICBhbmltYXRpb25UaW1lID0gNjAwLFxuICBwYXJ0aWNsZUNvdW50ID0gMTUsXG4gIHBhcnRpY2xlRGlzdGFuY2VzID0gWzkwLCAxMF0sXG4gIHBhcnRpY2xlUiA9IDEwMCxcbiAgdGltZVZhcmlhbmNlID0gMzAwLFxuICBjb2xvcnMgPSBbMSwgMiwgMywgMSwgMiwgMywgMSwgNF0sXG4gIGluaXRpYWxBY3RpdmVJbmRleCA9IDAsXG59KSA9PiB7XG4gIGNvbnN0IGNvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IG5hdlJlZiA9IHVzZVJlZjxIVE1MVUxpc3RFbGVtZW50PihudWxsKTtcbiAgY29uc3QgZmlsdGVyUmVmID0gdXNlUmVmPEhUTUxTcGFuRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IHRleHRSZWYgPSB1c2VSZWY8SFRNTFNwYW5FbGVtZW50PihudWxsKTtcbiAgY29uc3QgW2FjdGl2ZUluZGV4LCBzZXRBY3RpdmVJbmRleF0gPSB1c2VTdGF0ZTxudW1iZXI+KGluaXRpYWxBY3RpdmVJbmRleCk7XG5cbiAgY29uc3Qgbm9pc2UgPSAobiA9IDEpID0+IG4gLyAyIC0gTWF0aC5yYW5kb20oKSAqIG47XG5cbiAgY29uc3QgZ2V0WFkgPSAoXG4gICAgZGlzdGFuY2U6IG51bWJlcixcbiAgICBwb2ludEluZGV4OiBudW1iZXIsXG4gICAgdG90YWxQb2ludHM6IG51bWJlcixcbiAgKTogW251bWJlciwgbnVtYmVyXSA9PiB7XG4gICAgY29uc3QgYW5nbGUgPVxuICAgICAgKCgzNjAgKyBub2lzZSg4KSkgLyB0b3RhbFBvaW50cykgKiBwb2ludEluZGV4ICogKE1hdGguUEkgLyAxODApO1xuICAgIHJldHVybiBbZGlzdGFuY2UgKiBNYXRoLmNvcyhhbmdsZSksIGRpc3RhbmNlICogTWF0aC5zaW4oYW5nbGUpXTtcbiAgfTtcblxuICBjb25zdCBjcmVhdGVQYXJ0aWNsZSA9IChcbiAgICBpOiBudW1iZXIsXG4gICAgdDogbnVtYmVyLFxuICAgIGQ6IFtudW1iZXIsIG51bWJlcl0sXG4gICAgcjogbnVtYmVyLFxuICApID0+IHtcbiAgICAgY29uc3Qgcm90YXRlID0gbm9pc2UociAvIDEwKTtcbiAgICByZXR1cm4ge1xuICAgICAgc3RhcnQ6IGdldFhZKGRbMF0sIHBhcnRpY2xlQ291bnQgLSBpLCBwYXJ0aWNsZUNvdW50KSxcbiAgICAgIGVuZDogZ2V0WFkoZFsxXSArIG5vaXNlKDcpLCBwYXJ0aWNsZUNvdW50IC0gaSwgcGFydGljbGVDb3VudCksXG4gICAgICB0aW1lOiB0LFxuICAgICAgc2NhbGU6IDEgKyBub2lzZSgwLjIpLFxuICAgICAgY29sb3I6IGNvbG9yc1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBjb2xvcnMubGVuZ3RoKV0sXG4gICAgICByb3RhdGU6IHJvdGF0ZSA+IDAgPyAocm90YXRlICsgciAvIDIwKSAqIDEwIDogKHJvdGF0ZSAtIHIgLyAyMCkgKiAxMCxcbiAgICB9O1xuICB9O1xuXG4gIGNvbnN0IG1ha2VQYXJ0aWNsZXMgPSAoZWxlbWVudDogSFRNTEVsZW1lbnQpID0+IHtcbiAgICBjb25zdCBkOiBbbnVtYmVyLCBudW1iZXJdID0gcGFydGljbGVEaXN0YW5jZXM7XG4gICAgY29uc3QgciA9IHBhcnRpY2xlUjtcbiAgICBjb25zdCBidWJibGVUaW1lID0gYW5pbWF0aW9uVGltZSAqIDIgKyB0aW1lVmFyaWFuY2U7XG4gICAgZWxlbWVudC5zdHlsZS5zZXRQcm9wZXJ0eShcIi0tdGltZVwiLCBgJHtidWJibGVUaW1lfW1zYCk7XG5cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHBhcnRpY2xlQ291bnQ7IGkrKykge1xuICAgICAgY29uc3QgdCA9IGFuaW1hdGlvblRpbWUgKiAyICsgbm9pc2UodGltZVZhcmlhbmNlICogMik7XG4gICAgICBjb25zdCBwID0gY3JlYXRlUGFydGljbGUoaSwgdCwgZCwgcik7XG4gICAgICBlbGVtZW50LmNsYXNzTGlzdC5yZW1vdmUoXCJhY3RpdmVcIik7XG5cbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBjb25zdCBwYXJ0aWNsZSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzcGFuXCIpO1xuICAgICAgICBjb25zdCBwb2ludCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzcGFuXCIpO1xuICAgICAgICBwYXJ0aWNsZS5jbGFzc0xpc3QuYWRkKFwicGFydGljbGVcIik7XG4gICAgICAgIHBhcnRpY2xlLnN0eWxlLnNldFByb3BlcnR5KFwiLS1zdGFydC14XCIsIGAke3Auc3RhcnRbMF19cHhgKTtcbiAgICAgICAgcGFydGljbGUuc3R5bGUuc2V0UHJvcGVydHkoXCItLXN0YXJ0LXlcIiwgYCR7cC5zdGFydFsxXX1weGApO1xuICAgICAgICBwYXJ0aWNsZS5zdHlsZS5zZXRQcm9wZXJ0eShcIi0tZW5kLXhcIiwgYCR7cC5lbmRbMF19cHhgKTtcbiAgICAgICAgcGFydGljbGUuc3R5bGUuc2V0UHJvcGVydHkoXCItLWVuZC15XCIsIGAke3AuZW5kWzFdfXB4YCk7XG4gICAgICAgIHBhcnRpY2xlLnN0eWxlLnNldFByb3BlcnR5KFwiLS10aW1lXCIsIGAke3AudGltZX1tc2ApO1xuICAgICAgICBwYXJ0aWNsZS5zdHlsZS5zZXRQcm9wZXJ0eShcIi0tc2NhbGVcIiwgYCR7cC5zY2FsZX1gKTtcbiAgICAgICAgcGFydGljbGUuc3R5bGUuc2V0UHJvcGVydHkoXCItLWNvbG9yXCIsIGB2YXIoLS1jb2xvci0ke3AuY29sb3J9LCB3aGl0ZSlgKTtcbiAgICAgICAgcGFydGljbGUuc3R5bGUuc2V0UHJvcGVydHkoXCItLXJvdGF0ZVwiLCBgJHtwLnJvdGF0ZX1kZWdgKTtcblxuICAgICAgICBwb2ludC5jbGFzc0xpc3QuYWRkKFwicG9pbnRcIik7XG4gICAgICAgIHBhcnRpY2xlLmFwcGVuZENoaWxkKHBvaW50KTtcbiAgICAgICAgZWxlbWVudC5hcHBlbmRDaGlsZChwYXJ0aWNsZSk7XG4gICAgICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZSgoKSA9PiB7XG4gICAgICAgICAgZWxlbWVudC5jbGFzc0xpc3QuYWRkKFwiYWN0aXZlXCIpO1xuICAgICAgICB9KTtcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGVsZW1lbnQucmVtb3ZlQ2hpbGQocGFydGljbGUpO1xuICAgICAgICAgIH0gY2F0Y2gge1xuICAgICAgICAgICAgLy8gRG8gbm90aGluZ1xuICAgICAgICAgIH1cbiAgICAgICAgfSwgdCk7XG4gICAgICB9LCAzMCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZUVmZmVjdFBvc2l0aW9uID0gKGVsZW1lbnQ6IEhUTUxFbGVtZW50KSA9PiB7XG4gICAgaWYgKCFjb250YWluZXJSZWYuY3VycmVudCB8fCAhZmlsdGVyUmVmLmN1cnJlbnQgfHwgIXRleHRSZWYuY3VycmVudCkgcmV0dXJuO1xuICAgIGNvbnN0IGNvbnRhaW5lclJlY3QgPSBjb250YWluZXJSZWYuY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICBjb25zdCBwb3MgPSBlbGVtZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuXG4gICAgY29uc3Qgc3R5bGVzID0ge1xuICAgICAgbGVmdDogYCR7cG9zLnggLSBjb250YWluZXJSZWN0Lnh9cHhgLFxuICAgICAgdG9wOiBgJHtwb3MueSAtIGNvbnRhaW5lclJlY3QueX1weGAsXG4gICAgICB3aWR0aDogYCR7cG9zLndpZHRofXB4YCxcbiAgICAgIGhlaWdodDogYCR7cG9zLmhlaWdodH1weGAsXG4gICAgfTtcbiAgICBPYmplY3QuYXNzaWduKGZpbHRlclJlZi5jdXJyZW50LnN0eWxlLCBzdHlsZXMpO1xuICAgIE9iamVjdC5hc3NpZ24odGV4dFJlZi5jdXJyZW50LnN0eWxlLCBzdHlsZXMpO1xuICAgIHRleHRSZWYuY3VycmVudC5pbm5lclRleHQgPSBlbGVtZW50LmlubmVyVGV4dDtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVDbGljayA9IChcbiAgICBlOiBSZWFjdC5Nb3VzZUV2ZW50PEhUTUxBbmNob3JFbGVtZW50PixcbiAgICBpbmRleDogbnVtYmVyLFxuICApID0+IHtcbiAgICBjb25zdCBsaUVsID0gZS5jdXJyZW50VGFyZ2V0O1xuICAgIGlmIChhY3RpdmVJbmRleCA9PT0gaW5kZXgpIHJldHVybjtcblxuICAgIHNldEFjdGl2ZUluZGV4KGluZGV4KTtcbiAgICB1cGRhdGVFZmZlY3RQb3NpdGlvbihsaUVsKTtcblxuICAgIGlmIChmaWx0ZXJSZWYuY3VycmVudCkge1xuICAgICAgY29uc3QgcGFydGljbGVzID0gZmlsdGVyUmVmLmN1cnJlbnQucXVlcnlTZWxlY3RvckFsbChcIi5wYXJ0aWNsZVwiKTtcbiAgICAgIHBhcnRpY2xlcy5mb3JFYWNoKChwKSA9PiBmaWx0ZXJSZWYuY3VycmVudCEucmVtb3ZlQ2hpbGQocCkpO1xuICAgIH1cblxuICAgIGlmICh0ZXh0UmVmLmN1cnJlbnQpIHtcbiAgICAgIHRleHRSZWYuY3VycmVudC5jbGFzc0xpc3QucmVtb3ZlKFwiYWN0aXZlXCIpO1xuXG4gICAgICB2b2lkIHRleHRSZWYuY3VycmVudC5vZmZzZXRXaWR0aDtcbiAgICAgIHRleHRSZWYuY3VycmVudC5jbGFzc0xpc3QuYWRkKFwiYWN0aXZlXCIpO1xuICAgIH1cblxuICAgIGlmIChmaWx0ZXJSZWYuY3VycmVudCkge1xuICAgICAgbWFrZVBhcnRpY2xlcyhmaWx0ZXJSZWYuY3VycmVudCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUtleURvd24gPSAoXG4gICAgZTogUmVhY3QuS2V5Ym9hcmRFdmVudDxIVE1MQW5jaG9yRWxlbWVudD4sXG4gICAgaW5kZXg6IG51bWJlcixcbiAgKSA9PiB7XG4gICAgaWYgKGUua2V5ID09PSBcIkVudGVyXCIgfHwgZS5rZXkgPT09IFwiIFwiKSB7XG4gICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICBjb25zdCBsaUVsID0gZS5jdXJyZW50VGFyZ2V0LnBhcmVudEVsZW1lbnQ7XG4gICAgICBpZiAobGlFbCkge1xuICAgICAgICBoYW5kbGVDbGljayhcbiAgICAgICAgICB7XG4gICAgICAgICAgICBjdXJyZW50VGFyZ2V0OiBsaUVsLFxuICAgICAgICAgIH0gYXMgUmVhY3QuTW91c2VFdmVudDxIVE1MQW5jaG9yRWxlbWVudD4sXG4gICAgICAgICAgaW5kZXgsXG4gICAgICAgICk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFuYXZSZWYuY3VycmVudCB8fCAhY29udGFpbmVyUmVmLmN1cnJlbnQpIHJldHVybjtcbiAgICBjb25zdCBhY3RpdmVMaSA9IG5hdlJlZi5jdXJyZW50LnF1ZXJ5U2VsZWN0b3JBbGwoXCJsaVwiKVtcbiAgICAgIGFjdGl2ZUluZGV4XG4gICAgXSBhcyBIVE1MRWxlbWVudDtcbiAgICBpZiAoYWN0aXZlTGkpIHtcbiAgICAgIHVwZGF0ZUVmZmVjdFBvc2l0aW9uKGFjdGl2ZUxpKTtcbiAgICAgIHRleHRSZWYuY3VycmVudD8uY2xhc3NMaXN0LmFkZChcImFjdGl2ZVwiKTtcbiAgICB9XG5cbiAgICBjb25zdCByZXNpemVPYnNlcnZlciA9IG5ldyBSZXNpemVPYnNlcnZlcigoKSA9PiB7XG4gICAgICBjb25zdCBjdXJyZW50QWN0aXZlTGkgPSBuYXZSZWYuY3VycmVudD8ucXVlcnlTZWxlY3RvckFsbChcImxpXCIpW1xuICAgICAgICBhY3RpdmVJbmRleFxuICAgICAgXSBhcyBIVE1MRWxlbWVudDtcbiAgICAgIGlmIChjdXJyZW50QWN0aXZlTGkpIHtcbiAgICAgICAgdXBkYXRlRWZmZWN0UG9zaXRpb24oY3VycmVudEFjdGl2ZUxpKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIHJlc2l6ZU9ic2VydmVyLm9ic2VydmUoY29udGFpbmVyUmVmLmN1cnJlbnQpO1xuICAgIHJldHVybiAoKSA9PiByZXNpemVPYnNlcnZlci5kaXNjb25uZWN0KCk7XG4gIH0sIFthY3RpdmVJbmRleF0pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJnb29leS1uYXYtY29udGFpbmVyXCIgcmVmPXtjb250YWluZXJSZWZ9PlxuICAgICAgPG5hdj5cbiAgICAgICAgPHVsIHJlZj17bmF2UmVmfT5cbiAgICAgICAgICB7aXRlbXMubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPGxpIGtleT17aW5kZXh9IGNsYXNzTmFtZT17YWN0aXZlSW5kZXggPT09IGluZGV4ID8gXCJhY3RpdmVcIiA6IFwiXCJ9PlxuICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gaGFuZGxlQ2xpY2soZSwgaW5kZXgpfVxuICAgICAgICAgICAgICAgIG9uS2V5RG93bj17KGUpID0+IGhhbmRsZUtleURvd24oZSwgaW5kZXgpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvdWw+XG4gICAgICA8L25hdj5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImVmZmVjdCBmaWx0ZXJcIiByZWY9e2ZpbHRlclJlZn0gLz5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImVmZmVjdCB0ZXh0XCIgcmVmPXt0ZXh0UmVmfSAvPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgR29vZXlOYXY7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIkdvb2V5TmF2IiwiaXRlbXMiLCJhbmltYXRpb25UaW1lIiwicGFydGljbGVDb3VudCIsInBhcnRpY2xlRGlzdGFuY2VzIiwicGFydGljbGVSIiwidGltZVZhcmlhbmNlIiwiY29sb3JzIiwiaW5pdGlhbEFjdGl2ZUluZGV4IiwiY29udGFpbmVyUmVmIiwibmF2UmVmIiwiZmlsdGVyUmVmIiwidGV4dFJlZiIsImFjdGl2ZUluZGV4Iiwic2V0QWN0aXZlSW5kZXgiLCJub2lzZSIsIm4iLCJNYXRoIiwicmFuZG9tIiwiZ2V0WFkiLCJkaXN0YW5jZSIsInBvaW50SW5kZXgiLCJ0b3RhbFBvaW50cyIsImFuZ2xlIiwiUEkiLCJjb3MiLCJzaW4iLCJjcmVhdGVQYXJ0aWNsZSIsImkiLCJ0IiwiZCIsInIiLCJyb3RhdGUiLCJzdGFydCIsImVuZCIsInRpbWUiLCJzY2FsZSIsImNvbG9yIiwiZmxvb3IiLCJsZW5ndGgiLCJtYWtlUGFydGljbGVzIiwiZWxlbWVudCIsImJ1YmJsZVRpbWUiLCJzdHlsZSIsInNldFByb3BlcnR5IiwicCIsImNsYXNzTGlzdCIsInJlbW92ZSIsInNldFRpbWVvdXQiLCJwYXJ0aWNsZSIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsInBvaW50IiwiYWRkIiwiYXBwZW5kQ2hpbGQiLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJyZW1vdmVDaGlsZCIsInVwZGF0ZUVmZmVjdFBvc2l0aW9uIiwiY3VycmVudCIsImNvbnRhaW5lclJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJwb3MiLCJzdHlsZXMiLCJsZWZ0IiwieCIsInRvcCIsInkiLCJ3aWR0aCIsImhlaWdodCIsIk9iamVjdCIsImFzc2lnbiIsImlubmVyVGV4dCIsImhhbmRsZUNsaWNrIiwiZSIsImluZGV4IiwibGlFbCIsImN1cnJlbnRUYXJnZXQiLCJwYXJ0aWNsZXMiLCJxdWVyeVNlbGVjdG9yQWxsIiwiZm9yRWFjaCIsIm9mZnNldFdpZHRoIiwiaGFuZGxlS2V5RG93biIsImtleSIsInByZXZlbnREZWZhdWx0IiwicGFyZW50RWxlbWVudCIsImFjdGl2ZUxpIiwicmVzaXplT2JzZXJ2ZXIiLCJSZXNpemVPYnNlcnZlciIsImN1cnJlbnRBY3RpdmVMaSIsIm9ic2VydmUiLCJkaXNjb25uZWN0IiwiZGl2IiwiY2xhc3NOYW1lIiwicmVmIiwibmF2IiwidWwiLCJtYXAiLCJpdGVtIiwibGkiLCJhIiwiaHJlZiIsIm9uQ2xpY2siLCJvbktleURvd24iLCJsYWJlbCIsInNwYW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/GooeyNav/GooeyNav.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Csample-cafe-website-1%5C%5Csrc%5C%5Ccomponents%5C%5CGooeyNav%5C%5CGooeyNav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);